<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Shake & Match</title>
    <link rel="stylesheet" href="/admin/assets/admin-dashboard.css">
    <style>
        body {
            background: var(--bg-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            position: relative;
            overflow: hidden;
        }

        .login-container {
            background: var(--bg-secondary);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
            z-index: 1;
            border: 1px solid var(--border-color);
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo h1 {
            color: var(--text-primary);
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: var(--accent-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .logo p {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--accent-primary);
            background: var(--bg-secondary);
            box-shadow: 0 0 0 3px rgba(74, 125, 255, 0.1);
        }

        .form-group input.error {
            border-color: var(--accent-danger);
            background: var(--bg-danger-light);
        }

        .error-message {
            color: var(--accent-danger);
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .login-btn {
            width: 100%;
            padding: 1.2rem;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: var(--accent-primary-hover);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-btn .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        .login-btn.loading .spinner {
            display: inline-block;
        }

        .login-btn.loading .btn-text {
            opacity: 0.7;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: none;
        }

        .alert.show {
            display: block;
        }

        .alert.error {
            background: var(--bg-danger-light);
            color: var(--accent-danger);
            border: 1px solid var(--accent-danger);
        }

        .alert.success {
            background: var(--bg-success-light);
            color: var(--accent-success);
            border: 1px solid var(--accent-success);
        }

        .security-info {
            margin-top: 2rem;
            padding: 1rem;
            background: var(--bg-primary);
            border-radius: 10px;
            font-size: 0.875rem;
            color: var(--text-muted);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .security-info .icon {
            color: var(--accent-primary);
            margin-right: 0.5rem;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .logo h1 {
                font-size: 1.5rem;
            }
        }

        /* Rate limit warning */
        .rate-limit-warning {
            background: var(--bg-warning-light);
            color: var(--accent-warning);
            border: 1px solid var(--accent-warning);
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: none;
            text-align: center;
        }

        .rate-limit-warning.show {
            display: block;
        }
    </style>
        <!-- SVG Sprite for Icons -->
        <svg xmlns="http://www.w3.org/2000/svg" style="display:none">
            <symbol id="icon-lock" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 1a5 5 0 0 0-5 5v3H5a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V11a2 2 0 0 0-2-2h-2V6a5 5 0 0 0-5-5zm-3 8V6a3 3 0 0 1 6 0v3H9z"/>
            </symbol>
            <symbol id="icon-alert" viewBox="0 0 24 24" fill="currentColor">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v2h2v-2zm0-8h-2v6h2V10z"/>
            </symbol>
        </svg>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>
                Shake & Match
            </h1>
        </div>

        <div id="rateLimitWarning" class="rate-limit-warning" role="alert" aria-live="polite">
            <strong><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-alert"/></svg></span> Too many login attempts</strong><br>
            Please wait before trying again.
        </div>

        <div id="alert" class="alert">
            <span id="alertMessage"></span>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username">
                <div class="error-message" id="usernameError"></div>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
                <div class="error-message" id="passwordError"></div>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span class="btn-text">Sign In</span>
            </button>
        </form>

        <div class="security-info">
            <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-lock"/></svg></span>
            Secure admin access with JWT authentication
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const alert = document.getElementById('alert');
        const alertMessage = document.getElementById('alertMessage');
        const rateLimitWarning = document.getElementById('rateLimitWarning');

        // Check if already authenticated
        checkAuthStatus();

        async function checkAuthStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });

                if (response.ok) {
                    // Already authenticated, redirect to dashboard
                    window.location.href = '/admin/';
                }
            } catch (error) {
                // Not authenticated, stay on login page
                console.log('Not authenticated');
            }
        }

        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // Clear previous errors
            clearErrors();
            hideAlert();

            // Validate inputs
            if (!validateInputs(username, password)) {
                return;
            }

            // Show loading state
            setLoading(true);

            try {
                const response = await fetch(`${API_BASE}/api/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    showAlert('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 1000);
                } else {
                    if (response.status === 429) {
                        showRateLimitWarning();
                    }
                    showAlert(data.error || 'Login failed', 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('Network error. Please try again.', 'error');
            } finally {
                setLoading(false);
            }
        });

        function validateInputs(username, password) {
            let isValid = true;

            if (!username) {
                showFieldError('username', 'Username is required');
                isValid = false;
            } else if (username.length < 3) {
                showFieldError('username', 'Username must be at least 3 characters');
                isValid = false;
            }

            if (!password) {
                showFieldError('password', 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                showFieldError('password', 'Password must be at least 6 characters');
                isValid = false;
            }

            return isValid;
        }

        function showFieldError(fieldName, message) {
            const field = document.getElementById(fieldName);
            const errorElement = document.getElementById(fieldName + 'Error');

            field.classList.add('error');
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        function clearErrors() {
            const fields = ['username', 'password'];
            fields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                const errorElement = document.getElementById(fieldName + 'Error');

                field.classList.remove('error');
                errorElement.classList.remove('show');
            });
        }

        function showAlert(message, type) {
            alertMessage.textContent = message;
            alert.className = `alert ${type} show`;
        }

        function hideAlert() {
            alert.classList.remove('show');
        }

        function showRateLimitWarning() {
            rateLimitWarning.classList.add('show');
            setTimeout(() => {
                rateLimitWarning.classList.remove('show');
            }, 10000);
        }

        function setLoading(loading) {
            loginBtn.disabled = loading;
            if (loading) {
                loginBtn.classList.add('loading');
            } else {
                loginBtn.classList.remove('loading');
            }
        }

        // Clear rate limit warning when user starts typing
        document.getElementById('username').addEventListener('input', () => {
            rateLimitWarning.classList.remove('show');
        });

        document.getElementById('password').addEventListener('input', () => {
            rateLimitWarning.classList.remove('show');
        });
    </script>
</body>
</html>
