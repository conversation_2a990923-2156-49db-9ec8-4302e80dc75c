// screens/ViewProfileScreen.js
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  Alert,
  Modal,
  TextInput
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

const SERVER_ADDRESS = '**************:3000'; // Hardcoded server address

const ViewProfileScreen = ({ route, navigation }) => {
  // Extract params with defaults
  const { userId, username } = route.params || {};
  const forceRefresh = route.params?.forceRefresh || false;

  // Component state
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [errorMessage, setErrorMessage] = useState('');
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportDetails, setReportDetails] = useState('');
  const [selectedReason, setSelectedReason] = useState('');

  // Set the header title to the username
  useEffect(() => {
    navigation.setOptions({
      title: username || 'Profile',
    });
  }, [navigation, username, userId]);

  // Fetch profile data when component mounts or forceRefresh changes
  useEffect(() => {
    fetchProfileData(forceRefresh);
  }, [forceRefresh]);

  // Pull-to-refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchProfileData(true)
      .finally(() => setRefreshing(false));
  }, []);

  const fetchProfileData = async (skipCache = false) => {
    try {
      // Reset error message
      setErrorMessage('');

      if (!userId) {
        setErrorMessage('Missing user ID');
        setLoading(false);
        return;
      }

      // Check for cached profile first (if not skipping cache)
      if (!skipCache) {
        try {
          const cachedProfile = await AsyncStorage.getItem(`profile_${userId}`);

          if (cachedProfile) {
            const parsedProfile = JSON.parse(cachedProfile);

            if (parsedProfile) {
              setProfile(parsedProfile);

              // If we have a valid cache, we can show it right away
              // while we fetch the latest in the background
              setLoading(false);
            }
          }
        } catch (cacheError) {
          console.error('Error reading cache:', cacheError);
        }
      }

      // Try to fetch updated profile from server
      if (!skipCache) setLoading(true);

      // Add a timestamp to prevent caching issues
      const timestamp = new Date().getTime();
      const url = `http://${SERVER_ADDRESS}/api/profile/${userId}?t=${timestamp}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
      });

      if (response.ok) {
        const responseText = await response.text();

        try {
          const data = JSON.parse(responseText);

          if (data.profile) {
            // Add lastUpdated timestamp to the profile
            const profileWithTimestamp = {
              ...data.profile,
              lastUpdated: new Date().toISOString()
            };

            setProfile(profileWithTimestamp);

            // Cache the updated profile
            try {
              await AsyncStorage.setItem(`profile_${userId}`, JSON.stringify(profileWithTimestamp));
            } catch (cacheError) {
              console.error('Error saving to cache:', cacheError);
            }
          } else {
            setErrorMessage('Server returned empty profile data');
          }
        } catch (parseError) {
          console.error('Error parsing server response:', parseError, 'Response:', responseText);
          setErrorMessage(`Error parsing server response: ${parseError.message}`);
        }
      } else if (response.status === 404) {
        setErrorMessage('Profile not found on server');

        // Create a basic profile with the username
        if (username) {
          const basicProfile = {
            username,
            images: [],
            description: '',
            passions: [],
            age: null,
            lastUpdated: new Date().toISOString()
          };
          setProfile(basicProfile);
        }
      } else {
        const errorText = await response.text();
        console.error('Server error:', response.status, errorText);
        setErrorMessage(`Server error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      setErrorMessage(`Error fetching profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle image navigation
  const nextImage = () => {
    if (profile && profile.images && profile.images.length > 0) {
      setActiveImageIndex((prevIndex) =>
        prevIndex === profile.images.length - 1 ? 0 : prevIndex + 1
      );
    }
  };

  const prevImage = () => {
    if (profile && profile.images && profile.images.length > 0) {
      setActiveImageIndex((prevIndex) =>
        prevIndex === 0 ? profile.images.length - 1 : prevIndex - 1
      );
    }
  };

  // Handle reason selection (don't submit yet)
  const selectReason = (reason) => {
    setSelectedReason(reason);
  };

  // Handle actual report submission
  const submitReport = async () => {
    if (!selectedReason) {
      Alert.alert('Error', 'Please select a reason for reporting.');
      return;
    }

    try {
      const currentUserJSON = await AsyncStorage.getItem('user');
      const server = await AsyncStorage.getItem('serverAddress');

      if (!currentUserJSON || !server) {
        Alert.alert('Error', 'Please log in again.');
        setShowReportModal(false);
        return;
      }

      const currentUser = JSON.parse(currentUserJSON);
      if (!currentUser?.token) {
        Alert.alert('Error', 'Please log out and log in again to refresh your session.');
        setShowReportModal(false);
        return;
      }

      if (!userId) {
        Alert.alert('Error', 'Unable to identify user to report.');
        setShowReportModal(false);
        return;
      }

      const response = await fetch(`http://${server}/api/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser.token}`,
        },
        body: JSON.stringify({
          reportedUserId: userId,
          reason: selectedReason,
          details: reportDetails.trim()
        })
      });

      const data = await response.json().catch(() => ({}));

      if (!response.ok) {
        throw new Error(data.error || data.message || `HTTP ${response.status}`);
      }

      // Reset form and close modal
      setShowReportModal(false);
      setReportDetails('');
      setSelectedReason('');
      Alert.alert('Report Submitted', 'Thank you for your report. Our team will review it shortly.');

    } catch (error) {
      console.error('Report error:', error);
      Alert.alert('Error', error.message || 'Failed to submit report. Please try again.');
    }
  };

  // Render loading state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4e9af1" />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  // Render error state
  if (errorMessage) {
    return (
      <ScrollView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={60} color="#ff3b30" />
          <Text style={styles.errorTitle}>Error Loading Profile</Text>
          <Text style={styles.errorMessage}>{errorMessage}</Text>

          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => fetchProfileData(true)}
            >
              <Text style={styles.actionButtonText}>Retry</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.actionButtonText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  }

  // Render the profile data
  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#4e9af1']}
            tintColor="#4e9af1"
          />
        }
      >
      {/* Profile Photos Section */}
      <View style={styles.photoSection}>
        {profile && profile.images && profile.images.length > 0 ? (
          <>
            <Image
              source={{
                uri: profile.images[activeImageIndex].startsWith('data:')
                  ? profile.images[activeImageIndex]
                  : `data:image/jpeg;base64,${profile.images[activeImageIndex]}`
              }}
              style={styles.profileImage}
              resizeMode="cover"
            />

            {/* Image navigation buttons */}
            {profile.images.length > 1 && (
              <>
                <TouchableOpacity
                  style={[styles.imageNavButton, styles.prevButton]}
                  onPress={prevImage}
                >
                  <Ionicons name="chevron-back" size={28} color="#fff" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.imageNavButton, styles.nextButton]}
                  onPress={nextImage}
                >
                  <Ionicons name="chevron-forward" size={28} color="#fff" />
                </TouchableOpacity>

                <View style={styles.imageDots}>
                  {profile.images.map((_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.imageDot,
                        index === activeImageIndex && styles.activeImageDot
                      ]}
                    />
                  ))}
                </View>
              </>
            )}
          </>
        ) : (
          <View style={styles.noImageContainer}>
            <View style={styles.avatarCircle}>
              <Text style={styles.avatarText}>
                {username ? username.charAt(0).toUpperCase() : '?'}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Profile Info Section */}
      <View style={styles.infoSection}>
        <View style={styles.nameRow}>
          <Text style={styles.nameText}>
            {username || 'Unknown User'}
            {profile?.age ? ` , ${profile.age}` : ''}
          </Text>
        </View>

        {(!profile || Object.keys(profile).length === 0) && (
          <View style={styles.profileNotFoundContainer}>
            <Ionicons name="alert-circle-outline" size={24} color="#ff9500" />
            <Text style={styles.profileNotFoundText}>
              This user hasn't completed their profile yet
            </Text>
          </View>
        )}

        {/* Description */}
        {profile?.description ? (
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>{profile.description}</Text>
          </View>
        ) : (
          <View style={styles.emptyDescriptionContainer}>
            <Text style={styles.emptyText}>No description available</Text>
          </View>
        )}

        {/* Passions/Interests */}
        {profile?.passions && profile.passions.length > 0 ? (
          <View style={styles.passionsContainer}>
            <Text style={styles.sectionTitle}>Interests</Text>
            <View style={styles.passionTags}>
              {profile.passions.map((passion, index) => (
                <View key={index} style={styles.passionTag}>
                  <Text style={styles.passionText}>{passion}</Text>
                </View>
              ))}
            </View>
          </View>
        ) : null}
      </View>
      {/* Actions Section */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: '#ff3b30' }]}
          onPress={() => setShowReportModal(true)}
        >
          <Text style={styles.actionButtonText}>Report User</Text>
        </TouchableOpacity>
      </View>



      </ScrollView>

      {/* Report Modal */}
      <Modal
        visible={showReportModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowReportModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Report {username}</Text>
              <TouchableOpacity onPress={() => setShowReportModal(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <Text style={styles.modalSubtitle}>Why are you reporting this user?</Text>

            {/* Quick Report Reasons */}
            <View style={styles.reasonButtons}>
              {[
                'Harassment or bullying',
                'Inappropriate content',
                'Spam or fake profile',
                'Hate speech',
                'Scam or fraud',
                'Other'
              ].map((reason) => (
                <TouchableOpacity
                  key={reason}
                  style={[
                    styles.reasonButton,
                    selectedReason === reason && styles.reasonButtonSelected
                  ]}
                  onPress={() => selectReason(reason)}
                >
                  <Text style={styles.reasonButtonText}>{reason}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.detailsLabel}>Additional details (optional):</Text>
            <TextInput
              style={styles.detailsInput}
              placeholder="Provide more context..."
              multiline
              numberOfLines={3}
              value={reportDetails}
              onChangeText={setReportDetails}
              maxLength={500}
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowReportModal(false);
                  setSelectedReason('');
                  setReportDetails('');
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.modalButton,
                  styles.submitButton,
                  !selectedReason && styles.submitButtonDisabled
                ]}
                onPress={submitReport}
                disabled={!selectedReason}
              >
                <Text style={[
                  styles.submitButtonText,
                  !selectedReason && styles.submitButtonTextDisabled
                ]}>
                  Send Report
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  debugText: {
    fontSize: 12,
    color: '#999',
    marginTop: 5,
  },
  photoSection: {
    position: 'relative',
    width: '100%',
    height: 400,
    backgroundColor: '#eee',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  noImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#eee',
  },
  avatarCircle: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#4e9af1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 80,
    fontWeight: 'bold',
    color: 'white',
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    transform: [{ translateY: -25 }],
  },
  prevButton: {
    left: 10,
  },
  nextButton: {
    right: 10,
  },
  imageDots: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeImageDot: {
    backgroundColor: '#fff',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  infoSection: {
    padding: 20,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  nameText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  ageText: {
    fontSize: 24,
    fontWeight: 'normal',
  },
  profileNotFoundContainer: {
    backgroundColor: '#fff8e6',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ffcc66',
  },
  profileNotFoundText: {
    color: '#ff9500',
    fontSize: 14,
    marginLeft: 10,
    flex: 1,
  },
  descriptionContainer: {
    marginBottom: 20,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
  },
  emptyDescriptionContainer: {
    marginBottom: 20,
    padding: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    alignItems: 'center',
  },
  emptyText: {
    color: '#999',
    fontSize: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  passionsContainer: {
    marginTop: 10,
    marginBottom: 20,
  },
  passionTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  passionTag: {
    backgroundColor: '#e6f0ff',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#4e9af1',
  },
  passionText: {
    color: '#4e9af1',
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    marginHorizontal: 20,
    marginVertical: 20,
  },

  debugButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  debugContainer: {
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
    marginBottom: 20,
    width: '100%',
  },
  debugButtonText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 8,
  },
  debugTitle: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 10,
    color: '#666',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ff3b30',
    marginTop: 10,
    marginBottom: 10,
  },
  errorMessage: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  actionButton: {
    backgroundColor: '#4e9af1',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    margin: 5,
    flex: 1,
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  reasonButtons: {
    marginBottom: 20,
  },
  reasonButton: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  reasonButtonSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  reasonButtonText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  detailsLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  detailsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlignVertical: 'top',
    marginBottom: 20,
    minHeight: 80,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  submitButton: {
    backgroundColor: '#007AFF',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  submitButtonTextDisabled: {
    color: '#999',
  },
});

export default ViewProfileScreen;