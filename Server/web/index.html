<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Shake & Match</title>
    <link rel="stylesheet" href="/admin/assets/admin-dashboard.css">
    <!-- SVG Sprite for Icons -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display:none">
        <symbol id="icon-search" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 3a7 7 0 0 1 5.292 11.708l4 4a1 1 0 0 1-1.414 1.414l-4-4A7 7 0 1 1 10 3zm0 2a5 5 0 1 0 0 10A5 5 0 0 0 10 5z"/>
        </symbol>
        <symbol id="icon-bell" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2a4 4 0 0 1 4 4v1.09c2.282 1.167 4 3.757 4 6.91v2l2 2v1H2v-1l2-2v-2c0-3.153 1.718-5.743 4-6.91V6a4 4 0 0 1 4-4zm0 20a3 3 0 0 1-3-3h6a3 3 0 0 1-3 3z"/>
        </symbol>
        <symbol id="icon-gear" viewBox="0 0 24 24" fill="currentColor">
            <path d="M11.25 1.5h1.5l.84 1.68c.2.4.62.66 1.07.66h1.9l.42 1.64-1.46 1.06c-.38.27-.54.77-.39 1.22l.6 1.84-1.06 1.06-1.84-.6c-.45-.15-.95.01-1.22.39L12 14.46l-1.5-.01-1.06-1.06c-.27-.38-.77-.54-1.22-.39l-1.84.6-1.06-1.06.6-1.84c.15-.45-.01-.95-.39-1.22L3.07 5.48 3.49 3.84h1.9c.45 0 .87-.26 1.07-.66L7.3 1.5h1.5l.61 1.22c.2.4.62.66 1.07.66.45 0 .87-.26 1.07-.66L11.25 1.5zM12 9a3 3 0 110 6 3 3 0 010-6z"/>
        </symbol>
        <symbol id="icon-users" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16 11c1.657 0 3-1.79 3-4s-1.343-4-3-4-3 1.79-3 4 1.343 4 3 4zm-8 0c1.657 0 3-1.79 3-4S9.657 3 8 3 5 4.79 5 7s1.343 4 3 4zm0 2c-3.314 0-6 2.239-6 5v2h10v-2c0-2.761-2.686-5-6-5zm8 0c-.739 0-1.427.113-2.047.316 1.976 1.007 3.047 2.56 3.047 4.684V20h7v-2c0-2.761-2.686-5-6-5z"/>
        </symbol>
        <symbol id="icon-heart" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 21s-8-5.686-8-11a5 5 0 0 1 9-3 5 5 0 0 1 9 3c0 5.314-8 11-8 11z"/>
        </symbol>
        <symbol id="icon-mail" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2zm0 4-8 5-8-5V6l8 5 8-5v2z"/>
        </symbol>
        <symbol id="icon-eye" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 5c-7 0-11 7-11 7s4 7 11 7 11-7 11-7-4-7-11-7zm0 12a5 5 0 1 1 0-10 5 5 0 0 1 0 10z"/>
        </symbol>
        <symbol id="icon-pencil" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a1 1 0 0 0 0-1.41L18.37 3.29a1 1 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
        </symbol>
        <symbol id="icon-key" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14 3a5 5 0 1 0 3.546 8.546L22 16v3h-3v3h-3v-3h-3v-3l2.454-2.454A5 5 0 0 0 14 3z"/>
        </symbol>
        <symbol id="icon-dashboard" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 3h8v8H3V3zm10 0h8v8h-8V3zM3 13h8v8H3v-8zm10 0h8v8h-8v-8z"/>
        </symbol>
        <symbol id="icon-chart" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 3h2v18H3V3zm4 10h2v8H7v-8zm4-6h2v14h-2V7zm4 4h2v10h-2V11zm4-8h2v18h-2V3z"/>
        </symbol>
        <symbol id="icon-logout" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16 13v-2H7V8l-5 4 5 4v-3h9zm3-10H11a2 2 0 0 0-2 2v3h2V5h8v14h-8v-3H9v3a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z"/>
        </symbol>
    </svg>
        <symbol id="icon-alert" viewBox="0 0 24 24" fill="currentColor">
            <path d="M10.29 3.86L1.82 18a2 2 0 001.71 3h16.94a2 2 0 001.71-3L13.71 3.86a2 2 0 00-3.42 0zM12 9v4m0 4h.01"/>
        </symbol>

</head>

<body>
    <div class="app-layout">
        <!-- Enhanced Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon"></div>
                    <div class="logo-text">Shake&Match</div>
                </div>
            </div>
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <button class="nav-item active" onclick="showSection('dashboard')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-dashboard"/></svg></div>
                        Dashboard
                    </button>
                    <button class="nav-item" onclick="showSection('analytics')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-chart"/></svg></div>
                        Analytics
                    </button>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <button class="nav-item" onclick="showSection('users')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-users"/></svg></div>
                        Users
                    </button>
                    <button class="nav-item" onclick="showSection('matches')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-heart"/></svg></div>
                        Matches
                    </button>
                    <button class="nav-item" onclick="showSection('messages')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-mail"/></svg></div>
                        Messages
                    </button>
                    <button class="nav-item" onclick="showSection('reports')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-alert"/></svg></div>
                        Reports
                    </button>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <button class="nav-item" onclick="showSection('profile')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-users"/></svg></div>
                        Profile
                    </button>
                    <button class="nav-item" onclick="showSection('settings')">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-gear"/></svg></div>
                        Settings
                    </button>
                    <button class="nav-item" onclick="logout()">
                        <div class="nav-icon"><svg aria-hidden="true" focusable="false"><use href="#icon-logout"/></svg></div>
                        Logout
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Enhanced Top Bar -->
            <div class="top-bar">
                <div class="page-title">
                    <h1>Dashboard</h1>
                    <div class="breadcrumb">
                        <span>Admin</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">Dashboard</span>
                    </div>
                </div>

                <div class="search-bar">
                    <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-search"/></svg></span>
                    <input type="text" class="search-input" placeholder="Search..." aria-label="Search">
                </div>

                <div class="top-actions">
                    <button class="action-btn" title="Notifications" aria-label="Notifications">
                        <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-bell"/></svg></span>
                        <span class="notification-dot"></span>
                    </button>
                    <button class="action-btn" title="Settings" aria-label="Settings">
                        <span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-gear"/></svg></span>
                    </button>
                    <div class="user-profile">
                        <div class="user-avatar" id="userAvatar">A</div>
                        <div class="user-info">
                            <div class="user-name" id="currentUsername">Admin</div>
                            <div class="user-role">Administrator</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-area">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section">


                    <!-- Enhanced Stats Grid -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Active Users</div>
                                <div class="stat-icon active" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="activeUsers">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>12%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Total Users</div>
                                <div class="stat-icon users" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="totalUsers">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>18%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Total Matches</div>
                                <div class="stat-icon revenue" aria-hidden="true"><svg><use href="#icon-heart"/></svg></div>
                            </div>
                            <div class="stat-value" id="totalMatches">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>24%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Total Messages</div>
                                <div class="stat-icon growth" aria-hidden="true"><svg><use href="#icon-mail"/></svg></div>
                            </div>
                            <div class="stat-value" id="totalMessages">0</div>
                            <div class="stat-change positive">
                                <span class="change-arrow">↗</span>
                                <span>35%</span>
                                <span style="color: var(--text-muted); margin-left: 0.5rem;">from last month</span>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="charts-row">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Monthly Activity</h3>
                                <div class="chart-options">
                                    <button class="chart-option-btn active">Week</button>
                                    <button class="chart-option-btn">Month</button>
                                    <button class="chart-option-btn">Year</button>
                                </div>
                            </div>
                            <div class="chart-container" id="activityChart">
                                <div class="bar-chart">
                                    <div class="bar" style="height: 40%;"></div>
                                    <div class="bar" style="height: 70%;"></div>
                                    <div class="bar" style="height: 60%;"></div>
                                    <div class="bar" style="height: 90%;"></div>
                                    <div class="bar" style="height: 50%;"></div>
                                    <div class="bar" style="height: 45%;"></div>
                                    <div class="bar" style="height: 65%;"></div>
                                    <div class="bar" style="height: 55%;"></div>
                                    <div class="bar" style="height: 40%;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">User Distribution</h3>
                            </div>
                            <div class="circular-chart">
                                <div class="progress-ring">
                                    <svg>
                                        <defs>
                                            <linearGradient id="gradientChart" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style="stop-color: var(--accent-primary)" />
                                                <stop offset="50%" style="stop-color: var(--accent-secondary)" />
                                                <stop offset="100%" style="stop-color: var(--accent-purple)" />
                                            </linearGradient>
                                        </defs>
                                        <circle class="progress-bg" cx="90" cy="90" r="80"></circle>
                                        <circle class="progress-fill" cx="90" cy="90" r="80"></circle>
                                    </svg>
                                    <div class="progress-text">
                                        <div class="progress-value">68%</div>
                                        <div class="progress-label">Active</div>
                                    </div>
                                </div>
                                <div class="device-legend">
                                    <div class="legend-item">
                                        <div class="legend-label">
                                            <span class="legend-dot" style="background: var(--accent-primary);"></span>
                                            Active Users
                                        </div>
                                        <div class="legend-value">68%</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-label">
                                            <span class="legend-dot" style="background: var(--accent-secondary);"></span>
                                            Inactive Users
                                        </div>
                                        <div class="legend-value">25%</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-label">
                                            <span class="legend-dot" style="background: var(--accent-purple);"></span>
                                            Blocked Users
                                        </div>
                                        <div class="legend-value">7%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Section -->
                <div id="users-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2>User Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search users..." id="userSearch" onkeyup="searchUsers()">
                                <select id="roleFilter" class="form-select" onchange="filterUsers()">
                                    <option value="">All Roles</option>
                                    <option value="user">User</option>
                                    <option value="admin">Admin</option>
                                </select>
                                <select id="statusFilter" class="form-select" onchange="filterUsers()">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="blocked">Blocked</option>
                                    <option value="deleted">Deleted</option>
                                </select>
                                <button class="btn btn-primary" onclick="showCreateUserModal()">
                                    + Add User
                                </button>
                                <button class="btn btn-secondary" onclick="showBulkActionsModal()" id="bulkActionsBtn" style="display: none;">
                                    Bulk Actions
                                </button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()"></th>
                                        <th onclick="sortUsers('username')">Username ↕</th>
                                        <th onclick="sortUsers('email')">Email ↕</th>
                                        <th onclick="sortUsers('role')">Role ↕</th>
                                        <th onclick="sortUsers('accountStatus')">Status ↕</th>
                                        <th onclick="sortUsers('createdAt')">Created ↕</th>
                                        <th>Images</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="8" style="text-align: center; color: var(--text-muted);">Loading users...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container" id="usersPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="usersInfo">Showing 0 of 0 users</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="changePage(-1)" id="prevPageBtn">Previous</button>
                                <span id="pageInfo">Page 1 of 1</span>
                                <button class="btn btn-secondary" onclick="changePage(1)" id="nextPageBtn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Matches Section -->
                <div id="matches-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2>Match Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search matches..." id="matchSearch" onkeyup="searchMatches()">
                                <button class="btn btn-secondary" onclick="refreshMatches()">Refresh</button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Match ID</th>
                                        <th>User 1</th>
                                        <th>User 2</th>
                                        <th>Distance (km)</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="matchesTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; color: var(--text-muted);">Loading matches...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container" id="matchesPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="matchesInfo">Showing 0 of 0 matches</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="changeMatchesPage(-1)" id="prevMatchesPageBtn">Previous</button>

                                <span id="matchesPageInfo">Page 1 of 1</span>
                                <button class="btn btn-secondary" onclick="changeMatchesPage(1)" id="nextMatchesPageBtn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messages Section -->
                <div id="messages-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2>Message Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search messages..." id="messageSearch" onkeyup="searchMessages()">
                                <button class="btn btn-secondary" onclick="refreshMessages()">Refresh</button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Message ID</th>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Message</th>
                                        <th>Timestamp</th>
                                        <th>Read</th>
                                    </tr>
                                </thead>
                                <tbody id="messagesTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center; color: var(--text-muted);">Loading messages...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination-container" id="messagesPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="messagesInfo">Showing 0 of 0 messages</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-secondary" onclick="changeMessagesPage(-1)" id="prevMessagesPageBtn">Previous</button>
                                <span id="messagesPageInfo">Page 1 of 1</span>
                                <button class="btn btn-secondary" onclick="changeMessagesPage(1)" id="nextMessagesPageBtn">Next</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <div>
                                <h2 class="section-title">User Reports</h2>
                                <p class="section-subtitle">Review, warn, or block reported users</p>
                            </div>
                            <div class="actions" style="display:flex; gap:.5rem; align-items:center;">
                                <select id="reportsStatusFilter" class="input" style="width:auto">
                                    <option value="open">Open</option>
                                    <option value="resolved">Resolved</option>
                                    <option value="all">All</option>
                                </select>
                                <button class="btn btn-primary" onclick="loadReports(1)">Refresh</button>
                                <button class="btn btn-success" onclick="testReportsAPI()">Test API</button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Reported User</th>
                                        <th>Reporter</th>
                                        <th>Reason</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="reportsTableBody">
                                    <tr><td colspan="6" style="text-align:center;color:var(--text-muted)">Loading reports...</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pagination" id="reportsPagination" style="display:flex;gap:.5rem;justify-content:flex-end;margin-top:1rem;">
                            <button class="btn btn-secondary" onclick="changeReportsPage(-1)">Prev</button>
                            <span id="reportsPageInfo" style="align-self:center"></span>
                            <button class="btn btn-secondary" onclick="changeReportsPage(1)">Next</button>
                        </div>
                    </div>
                </div>

                <!-- Analytics Section -->
                <div id="analytics-section" class="content-section" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Active Users (Online)</div>
                                <div class="stat-icon active" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsActiveUsers">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Recent Matches (24h)</div>
                                <div class="stat-icon revenue" aria-hidden="true"><svg><use href="#icon-heart"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsRecentMatches">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">Recent Messages (24h)</div>
                                <div class="stat-icon growth" aria-hidden="true"><svg><use href="#icon-mail"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsRecentMessages">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-title">New Users (24h)</div>
                                <div class="stat-icon users" aria-hidden="true"><svg><use href="#icon-users"/></svg></div>
                            </div>
                            <div class="stat-value" id="analyticsNewUsers">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create New User</h3>
                <span class="close" onclick="closeModal('createUserModal')">&times;</span>
            </div>
            <form id="createUserForm">
                <div class="form-group">
                    <label class="form-label" for="newUsername">Username *</label>
                    <input type="text" id="newUsername" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="newEmail">Email</label>
                    <input type="email" id="newEmail" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label" for="newPassword">Password *</label>
                    <input type="password" id="newPassword" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="newRole">Role</label>
                    <select id="newRole" class="form-select">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="newAge">Age</label>
                    <input type="number" id="newAge" class="form-input" min="18" max="120">
                </div>
                <div class="form-group">
                    <label class="form-label" for="newDescription">Description</label>
                    <textarea id="newDescription" class="form-textarea" placeholder="User description..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('createUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Edit User</h3>
                <span class="close" onclick="closeModal('editUserModal')">&times;</span>
            </div>
            <form id="editUserForm">
                <input type="hidden" id="editUserId">
                <div class="form-group">
                    <label class="form-label" for="editUsername">Username *</label>
                    <input type="text" id="editUsername" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="editEmail">Email</label>
                    <input type="email" id="editEmail" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label" for="editRole">Role</label>
                    <select id="editRole" class="form-select">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="editAccountStatus">Account Status</label>
                    <select id="editAccountStatus" class="form-select">
                        <option value="active">Active</option>
                        <option value="blocked">Blocked</option>
                        <option value="deleted">Deleted</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="editAge">Age</label>
                    <input type="number" id="editAge" class="form-input" min="18" max="120">
                </div>
                <div class="form-group">
                    <label class="form-label" for="editDescription">Description</label>
                    <textarea id="editDescription" class="form-textarea" placeholder="User description..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-danger" onclick="deleteUser(document.getElementById('editUserId').value)">Delete User</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulkActionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Bulk Actions</h3>
                <span class="close" onclick="closeModal('bulkActionsModal')">&times;</span>
            </div>
            <div class="form-group">
                <label class="form-label">Selected Users: <span id="selectedUsersCount">0</span></label>
            </div>
            <div class="form-group">
                <label class="form-label" for="bulkAction">Action</label>
                <select id="bulkAction" class="form-select">
                    <option value="">Select Action</option>
                    <option value="block">Block Users</option>
                    <option value="unblock">Unblock Users</option>
                    <option value="delete">Soft Delete Users</option>
                    <option value="hard_delete">Permanently Delete Users</option>
                </select>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('bulkActionsModal')">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="executeBulkAction()">Execute Action</button>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div id="userDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">User Details</h3>
                <span class="close" onclick="closeModal('userDetailsModal')">&times;</span>
            </div>
            <div id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('userDetailsModal')">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = window.location.origin;

        // Global data storage
        let users = [];
        let matches = [];
        let messages = [];
        let reports = [];
        let reportsPagination = { page: 1, totalPages: 1, totalCount: 0 };
        let activeUsersData = [];
        let currentUser = null;
        let selectedUsers = new Set();

        // Pagination state
        let currentPage = 1;
        let totalPages = 1;
        let currentSort = { field: 'createdAt', order: 'desc' };
        let currentFilters = { search: '', role: '', accountStatus: '' };

        // Matches pagination
        let currentMatchesPage = 1;
        let totalMatchesPages = 1;

        // Messages pagination
        let currentMessagesPage = 1;
        let totalMessagesPages = 1;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Create user form
            document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);

            // Edit user form
            document.getElementById('editUserForm').addEventListener('submit', handleEditUser);

            // Close modals when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });
        }

        // Check authentication status
        async function checkAuthentication() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    window.location.href = '/admin/login';
                    return;
                }

                const data = await response.json();
                currentUser = data.user;

                // Update header with user info
                document.getElementById('currentUsername').textContent = currentUser.username;
                document.getElementById('userAvatar').textContent = currentUser.username.charAt(0).toUpperCase();

                // Load dashboard data
                loadDashboardData();
                setInterval(loadDashboardData, 30000);

            } catch (error) {
                console.error('Authentication check failed:', error);
                window.location.href = '/admin/login';
            }
        }

        // Logout function
        async function logout() {
            try {
                await fetch(`${API_BASE}/api/admin/logout`, {
                    method: 'POST',
                    credentials: 'include'
                });
                window.location.href = '/admin/login';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/admin/login';
            }
        }

        // Show section
        function showSection(sectionName) {
            // Update nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update breadcrumb
            const breadcrumb = document.querySelector('.breadcrumb-current');
            const titles = {
                'dashboard': 'Overview',
                'analytics': 'Analytics',
                'users': 'User Management',
                'matches': 'Matches',
                'messages': 'Messages',
                'reports': 'Reports',
                'profile': 'Profile',
                'settings': 'Settings'
            };
            if (breadcrumb) {
                breadcrumb.textContent = titles[sectionName] || 'Dashboard';
            }

            // Show/hide sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            const section = document.getElementById(`${sectionName}-section`);
            if (section) {
                section.style.display = 'block';
            } else {
                // Default to dashboard if section doesn't exist yet
                document.getElementById('dashboard-section').style.display = 'block';
            }
        }

        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadUsers(),
                    loadMatches(),
                    loadMessages(),
                    loadStats()
                ]);
                updateStats();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load users data with pagination and filtering
        async function loadUsers(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 50,
                    search: currentFilters.search,
                    role: currentFilters.role,
                    accountStatus: currentFilters.accountStatus,
                    sortBy: currentSort.field,
                    sortOrder: currentSort.order
                });

                const response = await fetch(`${API_BASE}/api/admin/users?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                users = data.users || [];

                // Update pagination info
                if (data.pagination) {
                    currentPage = data.pagination.currentPage;
                    totalPages = data.pagination.totalPages;
                    updateUsersPagination(data.pagination);
                }

                renderUsersTable();
            } catch (error) {
                console.error('Error loading users:', error);
                showError('Failed to load users');
            }
        }

        // Load matches data
        async function loadMatches(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 50
                });

                const response = await fetch(`${API_BASE}/api/admin/matches?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                matches = data.matches || [];

                // Update pagination info
                if (data.pagination) {
                    currentMatchesPage = data.pagination?.currentPage || page;
                    totalMatchesPages = data.pagination?.totalPages || 1;
                    updateMatchesPagination(data);
                }

                renderMatchesTable();
            } catch (error) {
                console.error('Error loading matches:', error);
                showError('Failed to load matches');
            }
        }

        // Load messages data
        async function loadMessages(page = 1) {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: 50
                });

                const response = await fetch(`${API_BASE}/api/admin/messages?${params}`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                messages = data.messages || [];

                // Update pagination info
                if (data.pagination) {
                    currentMessagesPage = data.pagination?.currentPage || page;
                    totalMessagesPages = data.pagination?.totalPages || 1;
                    updateMessagesPagination(data);
                }

                renderMessagesTable();
            } catch (error) {
                console.error('Error loading messages:', error);
                showError('Failed to load messages');
            }
        }

        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/stats`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();

                // Update analytics section
                document.getElementById('analyticsActiveUsers').textContent = data.activeUsers || 0;
                document.getElementById('analyticsRecentMatches').textContent = data.recentActivity?.matches || 0;
                document.getElementById('analyticsRecentMessages').textContent = data.recentActivity?.messages || 0;
                document.getElementById('analyticsNewUsers').textContent = data.recentActivity?.newUsers || 0;

                return data;
            } catch (error) {
                console.error('Error loading stats:', error);
                return null;
            }
        }

        // Render users table
        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: var(--text-muted);">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => {
                const statusClass = getStatusBadgeClass(user.accountStatus || 'active');
                const roleClass = user.role === 'admin' ? 'badge-warning' : 'badge-info';

                return `
                    <tr>
                        <td><input type="checkbox" class="user-checkbox" value="${user.id}" onchange="toggleUserSelection('${user.id}')"></td>
                        <td><strong>${user.username}</strong></td>
                        <td>${user.email || '-'}</td>
                        <td><span class="badge ${roleClass}">${(user.role || 'user').toUpperCase()}</span></td>
                        <td><span class="badge ${statusClass}">${(user.accountStatus || 'active').toUpperCase()}</span></td>
                        <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                        <td>${user.imageCount || 0}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-icon btn-secondary" onclick="viewUserDetails('${user.id}')" title="View Details" aria-label="View Details"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-eye"/></svg></span></button>
                                <button class="btn btn-icon btn-secondary" onclick="editUser('${user.id}')" title="Edit" aria-label="Edit"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-pencil"/></svg></span></button>
                                <button class="btn btn-icon btn-secondary" onclick="resetUserPassword('${user.id}')" title="Reset Password" aria-label="Reset Password"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-key"/></svg></span></button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Render matches table
        function renderMatchesTable() {
            const tbody = document.getElementById('matchesTableBody');
            if (!tbody) return;

            if (matches.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--text-muted);">No matches found</td></tr>';
                return;
            }

            tbody.innerHTML = matches.map(match => `
                <tr>
                    <td><code>${match.id.substring(0, 8)}...</code></td>
                    <td><strong>${match.user1Username}</strong></td>
                    <td><strong>${match.user2Username}</strong></td>
                    <td>${match.distance} km</td>
                    <td>${new Date(match.createdAt).toLocaleDateString()}</td>
                    <td>
                        <button class="btn btn-icon btn-secondary" onclick="viewMatchDetails('${match.id}')" title="View Details" aria-label="View Details"><span class="icon icon-md" aria-hidden="true"><svg><use href="#icon-eye"/></svg></span></button>
                    </td>
                </tr>
            `).join('');
        }

        // Render messages table
        function renderMessagesTable() {
            const tbody = document.getElementById('messagesTableBody');
            if (!tbody) return;

            if (messages.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--text-muted);">No messages found</td></tr>';
                return;
            }

            tbody.innerHTML = messages.map(message => `
                <tr>
                    <td><code>${message.messageId?.substring(0, 8) || 'N/A'}...</code></td>
                    <td><strong>${message.senderUsername || 'Unknown'}</strong></td>
                    <td><strong>${message.receiverUsername || 'Unknown'}</strong></td>
                    <td>${truncateText(message.text || '', 50)}</td>
                    <td>${new Date(message.timestamp).toLocaleString()}</td>
                    <td><span class="badge ${message.read ? 'badge-success' : 'badge-warning'}">${message.read ? 'READ' : 'UNREAD'}</span></td>
                </tr>
            `).join('');
        }

        // Helper function to get status badge class
        function getStatusBadgeClass(status) {
            switch (status.toLowerCase()) {
                case 'active': return 'badge-success';
                case 'blocked': return 'badge-danger';
                case 'deleted': return 'badge-warning';
                default: return 'badge-info';
            }
        }

        // Helper function to truncate text
        function truncateText(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // Pagination functions
        function updateUsersPagination(pagination) {
            const paginationContainer = document.getElementById('usersPagination');
            const usersInfo = document.getElementById('usersInfo');
            const pageInfo = document.getElementById('pageInfo');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');

            if (paginationContainer) {
                paginationContainer.style.display = 'flex';
                usersInfo.textContent = `Showing ${users.length} of ${pagination.totalUsers} users`;
                pageInfo.textContent = `Page ${pagination.currentPage} of ${pagination.totalPages}`;

                prevBtn.disabled = !pagination.hasPrevPage;
                nextBtn.disabled = !pagination.hasNextPage;
            }
        }

        function updateMatchesPagination(data) {
            const paginationContainer = document.getElementById('matchesPagination');
            const matchesInfo = document.getElementById('matchesInfo');
            const pageInfo = document.getElementById('matchesPageInfo');
            const prevBtn = document.getElementById('prevMatchesPageBtn');
            const nextBtn = document.getElementById('nextMatchesPageBtn');

            if (paginationContainer && data.pagination) {
                paginationContainer.style.display = 'flex';
                matchesInfo.textContent = `Showing ${matches.length} of ${data.totalCount} matches`;
                pageInfo.textContent = `Page ${data.page} of ${data.totalPages}`;

                prevBtn.disabled = data.page <= 1;
                nextBtn.disabled = data.page >= data.totalPages;
            }
        }

        function updateMessagesPagination(data) {
            const paginationContainer = document.getElementById('messagesPagination');
            const messagesInfo = document.getElementById('messagesInfo');
            const pageInfo = document.getElementById('messagesPageInfo');
            const prevBtn = document.getElementById('prevMessagesPageBtn');
            const nextBtn = document.getElementById('nextMessagesPageBtn');

            if (paginationContainer && data.pagination) {
                paginationContainer.style.display = 'flex';
                messagesInfo.textContent = `Showing ${messages.length} of ${data.totalCount} messages`;
                pageInfo.textContent = `Page ${data.page} of ${data.totalPages}`;

                prevBtn.disabled = data.page <= 1;
                nextBtn.disabled = data.page >= data.totalPages;
            }
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                loadUsers(newPage);
            }
        }

        function changeMatchesPage(direction) {
            const newPage = currentMatchesPage + direction;
            if (newPage >= 1 && newPage <= totalMatchesPages) {
                loadMatches(newPage);
            }
        }

        function changeMessagesPage(direction) {
            const newPage = currentMessagesPage + direction;
            if (newPage >= 1 && newPage <= totalMessagesPages) {
                loadMessages(newPage);
            }
        }

        // Search and filter functions
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value;
            currentFilters.search = searchTerm;
            currentPage = 1;
            loadUsers(1);
        }

        function filterUsers() {
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            currentFilters.role = roleFilter;
            currentFilters.accountStatus = statusFilter;
            currentPage = 1;
            loadUsers(1);
        }

        function sortUsers(field) {
            if (currentSort.field === field) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.order = 'asc';
            }
            currentPage = 1;
            loadUsers(1);
        }

        function searchMatches() {
            console.log('Match search not implemented yet');
        }

        function searchMessages() {
            console.log('Message search not implemented yet');
        }

        function refreshMatches() {
            loadMatches(currentMatchesPage);
        }

        function refreshMessages() {
            loadMessages(currentMessagesPage);
        }

        // User selection functions
        function toggleUserSelection(userId) {
            const checkbox = document.querySelector(`input[value="${userId}"]`);
            if (checkbox.checked) {
                selectedUsers.add(userId);
            } else {
                selectedUsers.delete(userId);
            }
            updateBulkActionsButton();
        }

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllUsers');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');

            userCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                if (selectAllCheckbox.checked) {
                    selectedUsers.add(checkbox.value);
                } else {
                    selectedUsers.delete(checkbox.value);
                }
            });
            updateBulkActionsButton();
        }

        function updateBulkActionsButton() {
            const bulkActionsBtn = document.getElementById('bulkActionsBtn');
            if (selectedUsers.size > 0) {
                bulkActionsBtn.style.display = 'inline-block';
            } else {
                bulkActionsBtn.style.display = 'none';
            }
        }

        // Modal functions
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function showCreateUserModal() {
            document.getElementById('createUserForm').reset();
            showModal('createUserModal');
        }

        function showBulkActionsModal() {
            document.getElementById('selectedUsersCount').textContent = selectedUsers.size;
            showModal('bulkActionsModal');
        }

        // Update statistics
        function updateStats() {
            const totalUsersEl = document.getElementById('totalUsers');
            const activeUsersEl = document.getElementById('activeUsers');
            const totalMatchesEl = document.getElementById('totalMatches');

            if (totalUsersEl) totalUsersEl.textContent = users.length > 0 ? `${users.length}` : '0';
            if (activeUsersEl) activeUsersEl.textContent = '0';
            if (totalMatchesEl) totalMatchesEl.textContent = matches.length > 0 ? `${matches.length}` : '0';
        }

        // Error handling
        function showError(message) {
            console.error(message);
            alert(message);
        }

        function showSuccess(message) {
            console.log(message);
            alert(message);
        }

        // User management functions
        async function handleCreateUser(event) {
            event.preventDefault();

            const formData = {
                username: document.getElementById('newUsername').value,
                email: document.getElementById('newEmail').value || undefined,
                password: document.getElementById('newPassword').value,
                role: document.getElementById('newRole').value,
                age: parseInt(document.getElementById('newAge').value) || undefined,
                description: document.getElementById('newDescription').value || ''
            };

            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to create user');
                }

                const result = await response.json();
                showSuccess('User created successfully');
                closeModal('createUserModal');
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error creating user:', error);
                showError(error.message);
            }
        }

        async function editUser(userId) {
            try {
                const user = users.find(u => u.id === userId);
                if (!user) {
                    showError('User not found');
                    return;
                }

                document.getElementById('editUserId').value = userId;
                document.getElementById('editUsername').value = user.username;
                document.getElementById('editEmail').value = user.email || '';
                document.getElementById('editRole').value = user.role || 'user';
                document.getElementById('editAccountStatus').value = user.accountStatus || 'active';
                document.getElementById('editAge').value = user.age || '';
                document.getElementById('editDescription').value = user.description || '';

                showModal('editUserModal');
            } catch (error) {
                console.error('Error loading user for edit:', error);
                showError('Failed to load user data');
            }
        }

        async function handleEditUser(event) {
            event.preventDefault();

            const userId = document.getElementById('editUserId').value;
            const formData = {
                username: document.getElementById('editUsername').value,
                email: document.getElementById('editEmail').value || undefined,
                role: document.getElementById('editRole').value,
                accountStatus: document.getElementById('editAccountStatus').value,
                age: parseInt(document.getElementById('editAge').value) || undefined,
                description: document.getElementById('editDescription').value || ''
            };

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to update user');
                }

                const result = await response.json();
                showSuccess('User updated successfully');
                closeModal('editUserModal');
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error updating user:', error);
                showError(error.message);
            }
        }

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || 'Failed to delete user');
                }

                showSuccess('User deleted successfully');
                closeModal('editUserModal');
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error deleting user:', error);
                showError(error.message);
            }
        }

        async function resetUserPassword(userId) {
            const newPassword = prompt('Enter new password for user:');
            if (!newPassword) return;

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}/password`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ password: newPassword })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to reset password');
                }
            // Reports: fetch and render
            async function loadReports(page = 1) {
                try {
                    const status = document.getElementById('reportsStatusFilter')?.value || 'open';
                    const params = new URLSearchParams({ page, limit: 20, status });
                    const url = `${API_BASE}/api/admin/reports?${params}`;
                    console.log('Loading reports from:', url);
                    const response = await fetch(url, { credentials: 'include' });
                    console.log('Reports response status:', response.status);
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();
                    console.log('Reports data:', data);
                    reports = data.reports || [];
                    reportsPagination = { page: data.page, totalPages: data.totalPages, totalCount: data.totalCount };
                    renderReportsTable();
                } catch (e) {
                    console.error('Failed to load reports', e);
                    const tbody = document.getElementById('reportsTableBody');
                    if (tbody) {
                        tbody.innerHTML = `<tr><td colspan="6" style="text-align:center;color:red">Error loading reports: ${e.message}</td></tr>`;
                    }
                }
            }

            function changeReportsPage(delta) {
                const next = Math.min(Math.max(1, (reportsPagination.page || 1) + delta), reportsPagination.totalPages || 1);
                if (next !== reportsPagination.page) loadReports(next);
            }

            function renderReportsTable() {
                const tbody = document.getElementById('reportsTableBody');
                const pageInfo = document.getElementById('reportsPageInfo');
                if (!tbody) return;
                if (!reports || reports.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align:center;color:var(--text-muted)">No reports</td></tr>';
                    pageInfo.textContent = `Page ${reportsPagination.page || 1} of ${reportsPagination.totalPages || 1}`;
                    return;
                }
                tbody.innerHTML = reports.map(r => {
                    const reported = r.reportedUserId || {};
                    const reporter = r.reporterId || {};
                    const statusBadge = r.status === 'open' ? 'badge-warning' : 'badge-success';
                    const statusText = r.status.charAt(0).toUpperCase() + r.status.slice(1);
                    const date = new Date(r.createdAt).toLocaleString();
                    return `
                        <tr>
                            <td>${reported.username || 'N/A'}</td>
                            <td>${reporter.username || 'N/A'}</td>
                            <td title="${escapeHtml(r.reason)}">${escapeHtml(r.reason)}</td>
                            <td>${date}</td>
                            <td><span class="badge ${statusBadge}">${statusText}</span></td>
                            <td>
                                ${r.status === 'open' ? `
                                <button class="btn btn-small" onclick="resolveReport('${r._id}', 'warning')">Warn</button>
                                <button class="btn btn-small btn-danger" onclick="resolveReport('${r._id}', 'block')">Block</button>
                                <button class="btn btn-small btn-secondary" onclick="resolveReport('${r._id}', 'ignore')">Ignore</button>
                                ` : `<span class="text-muted">Resolved</span>`}
                            </td>
                        </tr>
                    `;
                }).join('');
                pageInfo.textContent = `Page ${reportsPagination.page || 1} of ${reportsPagination.totalPages || 1}`;
            }

            async function resolveReport(reportId, action) {
                const notes = prompt(`Add notes for ${action} (optional):`, '');
                try {
                    const response = await fetch(`${API_BASE}/api/admin/reports/${reportId}/resolve`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        credentials: 'include',
                        body: JSON.stringify({ action, notes })
                    });
                    if (!response.ok) {
                        const err = await response.json().catch(() => ({}));
                        throw new Error(err.error || `HTTP ${response.status}`);
                    }
                    await loadReports(reportsPagination.page || 1);
                    // Refresh users so warningCount/accountStatus are updated in the users view
                    await loadUsers(1);
                } catch (e) {
                    alert('Failed to resolve report: ' + e.message);
                }
            }

            function escapeHtml(str = '') {
                return String(str)
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;');
            }

            // Hook section switching to load reports when opened
            const originalShowSection = showSection;
            showSection = function(sectionName) {
                originalShowSection(sectionName);
                if (sectionName === 'reports') {
                    console.log('Loading reports section...');
                    loadReports(1);
                }
            }

            // Add event listener for reports status filter
            document.addEventListener('DOMContentLoaded', function() {
                const statusFilter = document.getElementById('reportsStatusFilter');
                if (statusFilter) {
                    statusFilter.addEventListener('change', function() {
                        loadReports(1);
                    });
                }
            });

        // Test function to debug reports API (global scope)
        async function testReportsAPI() {
            console.log('=== TESTING REPORTS API ===');
            try {
                // Test 1: Check if we're authenticated
                console.log('Testing authentication...');
                const authTest = await fetch(`${API_BASE}/api/admin/users?page=1&limit=1`, { credentials: 'include' });
                console.log('Auth test status:', authTest.status);

                // Test 2: Try to fetch reports with different parameters
                console.log('Testing reports API...');
                const tests = [
                    { status: 'all', page: 1, limit: 50 },
                    { status: 'open', page: 1, limit: 50 },
                    { status: 'resolved', page: 1, limit: 50 }
                ];

                for (const test of tests) {
                    const params = new URLSearchParams(test);
                    const url = `${API_BASE}/api/admin/reports?${params}`;
                    console.log(`Testing: ${url}`);

                    const response = await fetch(url, { credentials: 'include' });
                    console.log(`Status: ${response.status}`);

                    if (response.ok) {
                        const data = await response.json();
                        console.log(`Data:`, data);
                    } else {
                        const errorText = await response.text();
                        console.log(`Error:`, errorText);
                    }
                }
            } catch (error) {
                console.error('Test failed:', error);
            }
        }



        async function executeBulkAction() {
            const action = document.getElementById('bulkAction').value;
            if (!action) {
                showError('Please select an action');
                return;
            }

            const userIds = Array.from(selectedUsers);
            if (userIds.length === 0) {
                showError('No users selected');
                return;
            }

            const confirmMessage = `Are you sure you want to ${action} ${userIds.length} user(s)?`;
            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/bulk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ action, userIds })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Bulk operation failed');
                }

                const result = await response.json();
                showSuccess(`Bulk ${action} completed: ${result.affected} users affected`);
                closeModal('bulkActionsModal');
                selectedUsers.clear();
                updateBulkActionsButton();
                loadUsers(currentPage);
            } catch (error) {
                console.error('Error in bulk operation:', error);
                showError(error.message);
            }
        }

        async function viewUserDetails(userId) {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users/${userId}/activity`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error('Failed to load user details');
                }

                const data = await response.json();
                const user = data.user;
                const stats = data.stats;
                const recentMatches = data.recentMatches;

                const detailsHtml = `
                    <div class="user-details">
                        <h4>User Information</h4>
                        <p><strong>Username:</strong> ${user.username}</p>
                        <p><strong>Email:</strong> ${user.email || 'Not provided'}</p>
                        <p><strong>Role:</strong> ${user.role || 'user'}</p>
                        <p><strong>Status:</strong> ${user.accountStatus || 'active'}</p>
                        <p><strong>Age:</strong> ${user.age || 'Not provided'}</p>
                        <p><strong>Description:</strong> ${user.description || 'No description'}</p>
                        <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleString()}</p>

                        <h4>Statistics</h4>
                        <p><strong>Total Matches:</strong> ${stats.totalMatches}</p>
                        <p><strong>Total Messages:</strong> ${stats.totalMessages}</p>
                        <p><strong>Sent Messages:</strong> ${stats.sentMessages}</p>
                        <p><strong>Received Messages:</strong> ${stats.receivedMessages}</p>
                        <p><strong>Account Age:</strong> ${stats.accountAge} days</p>

                        <h4>Recent Matches</h4>
                        ${recentMatches.length > 0 ?
                            recentMatches.map(match =>
                                `<p>• ${match.partner} (${new Date(match.createdAt).toLocaleDateString()})</p>`
                            ).join('') :
                            '<p>No recent matches</p>'
                        }
                    </div>
                `;

                document.getElementById('userDetailsContent').innerHTML = detailsHtml;
                showModal('userDetailsModal');
            } catch (error) {
                console.error('Error loading user details:', error);
                showError('Failed to load user details');
            }
        }

        function viewMatchDetails(matchId) {
            console.log('Match details for:', matchId);
            showError('Match details view not implemented yet');
        }
    </script>
</body>
</html>